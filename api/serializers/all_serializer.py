from rest_framework import serializers

from api.models.comment_model import (
    Comment,
    FollowingRelationships,
    Like,
    Reply,
    Review,
)
from api.models.company_model import Company, CompanyCategory, CompanySize
from api.models.document_model import Document, DocumentType
from api.models.post_model import Post, PostImage
from api.models.product_model import Product, ProductCategory, ProductImage
from api.models.representative import Representative, RepresentativeCategory
from api.models.service_model import Service, ServiceCategory, ServiceImage
from api.serializers.user_serializer import BasicUserSerializer, UserSerializer
from authentication.models import User


class LikeMixin:

    def get_likes(self, obj):
        likes = obj.likes.all()
        return LikeSerializer(likes, many=True).data
    

class DocumentSerializer(serializers.HyperlinkedModelSerializer):
    type = serializers.SlugRelatedField(queryset=DocumentType.objects.all(), slug_field='name')
    
    company = serializers.SlugRelatedField(queryset=Company.objects.all(), slug_field='company_name')
    
    class Meta:
        model = Document
        fields = ['id', 'type', 'document', 'company']


class DocumentTypeSerializer(serializers.HyperlinkedModelSerializer):

    url = serializers.HyperlinkedRelatedField(
                    many=True,
                    read_only=True,
                    view_name='api:document-type-detail')

    class Meta:
        model = DocumentType
        fields = ['id', 'name', 'type', "url"]


class CompanyMiniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ['id', 'company_name', 'logo'] 


class ProductCategorySerializer(serializers.HyperlinkedModelSerializer):
    products = serializers.HyperlinkedRelatedField(many=True, read_only=True, view_name='api:product-detail')
    url = serializers.HyperlinkedRelatedField(
                    many=True,
                    read_only=True,
                    view_name='api:product-category-detail')

    class Meta:
        model = ProductCategory
        fields = ['id', 'url', 'name', 'products']


class ProductImageSerializer(serializers.HyperlinkedModelSerializer):
    url = serializers.HyperlinkedRelatedField(view_name='api:product-image-detail', read_only=True)
    product = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all(), required=False, allow_null=True)
    temporary = serializers.BooleanField(default=True)

    class Meta:
        model = ProductImage
        fields = ['id', 'url', 'caption', 'image', 'product', 'temporary']

    def create(self, validated_data):
        """
        Ensure temporary=True when creating new images without a product.
        """
        if 'temporary' not in validated_data and not validated_data.get('product'):
            validated_data['temporary'] = True
        return super().create(validated_data)


class ProductSerializer(serializers.HyperlinkedModelSerializer):
    images = ProductImageSerializer(many=True, read_only=True)
    category = serializers.SlugRelatedField(queryset=ProductCategory.objects.all(), slug_field='name')
    company = CompanyMiniSerializer(read_only=True)
    likes = serializers.SerializerMethodField()
    class Meta:
        model = Product
        fields = [
            'id', 'title', 'sub_title', 'category', 'description', 'date_created', 'date_updated', 'featured', 'company', 'likes', 'images'
        ]
        
    def get_likes(self, obj):
        likes = obj.likes.all()
        return LikeSerializer(likes, many=True).data

    
class ServiceCategorySerializer(serializers.HyperlinkedModelSerializer):
    services = serializers.HyperlinkedRelatedField(many=True, read_only=True, view_name='api:service-detail')
    url = serializers.HyperlinkedRelatedField(many=True, read_only=True, view_name='api:service-category-detail')

    class Meta:
        model = ServiceCategory
        fields = ['id', 'url', 'name', 'services']


class BasicUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            "id",
            "full_name",
            "email",
            "avatar",
            "verified",
            "country",
            "city",
            "phone_number",
            "region",
            "address",
        ]


class BasicCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ["id", "company_name", "slug", "logo"]


class ServiceImageSerializer(serializers.HyperlinkedModelSerializer):
    url = serializers.HyperlinkedRelatedField(view_name='api:service-image-detail', read_only=True)
    service = serializers.PrimaryKeyRelatedField(queryset=Service.objects.all())

    class Meta:
        model = ServiceImage
        fields = ['id', 'url', 'caption', 'image', 'service']


class ServiceSerializer(serializers.HyperlinkedModelSerializer):
    images = ServiceImageSerializer(many=True, read_only=True)
    category = serializers.SlugRelatedField(queryset=ServiceCategory.objects.all(), slug_field='name')
    company = CompanyMiniSerializer(read_only=True)
    likes = serializers.SerializerMethodField()

    class Meta:
        model = Service
        fields = [
            'id', 'title', 'sub_title', 'category', 'description', 'date_created', 'date_updated', 'featured', 'company', 'likes', 'images'
        ]
        
    def get_likes(self, obj):
        likes = obj.likes.all()
        return LikeSerializer(likes, many=True).data


class ReviewSerializer(serializers.ModelSerializer):
    company_id = serializers.IntegerField(source="company.id", read_only=True)
    reviewed_at = serializers.DateTimeField(read_only=True)
    user = UserSerializer(read_only=True)

    class Meta:
        model = Review
        fields = (
            "id", "content", 'user', "reviewed_at", "company_id"
        )


class CompanySizeSerializer(serializers.HyperlinkedModelSerializer):
    companies = serializers.HyperlinkedRelatedField(many=True, read_only=True, view_name='api:company-detail', lookup_field='slug')
    url = serializers.HyperlinkedRelatedField(view_name='api:company-size-detail', read_only=True)

    class Meta:
        model = CompanySize
        fields = ['id', 'url', 'size', 'companies']


class CompanyCategorySerializer(serializers.ModelSerializer):
    companies = serializers.HyperlinkedRelatedField(many=True, read_only=True, view_name='api:company-detail', lookup_field='slug')
    url = serializers.HyperlinkedRelatedField(view_name='api:company-category-detail', read_only=True)

    class Meta:
        model = CompanyCategory
        fields = ['id', "url", 'name', 'companies']


class CompanySerializer(serializers.HyperlinkedModelSerializer):
    organization_type = serializers.CharField()
    company_size = serializers.CharField()
    documents = DocumentSerializer(many=True, read_only=True)
    products = ProductSerializer(many=True, read_only=True)
    services = ServiceSerializer(many=True, read_only=True)
    profile = serializers.ReadOnlyField(source='profile.email')
    followers_count = serializers.IntegerField(read_only=True)
    following_count = serializers.IntegerField(read_only=True)
    url = serializers.HyperlinkedRelatedField(view_name='api:company-detail', read_only=True, lookup_field='slug')
    followers = serializers.SerializerMethodField()
    following = serializers.SerializerMethodField()
    reviews = serializers.SerializerMethodField()
    user = serializers.SerializerMethodField()
    
    class Meta:
        model = Company
        fields = [
            'id', 'url', 'profile', 'user', 'company_name', 'organization_type', 'about', 'tag_line', 'company_size', 
            'logo', 'banner', 'email', 'office_address', 'country', 'state', 'city', 'website', 'verify', "followers_count", 
            "following_count", 'documents', 'products', 'services', 'followers', 'following', 'reviews', 'registration_date', 
            'registration_number', 'annual_revenue', 'slug'
        ]
        
    def get_followers(self, obj):
        followers = FollowingRelationships.objects.filter(company_following=obj)
        return FollowSerializer(followers, many=True).data
    
    def get_following(self, obj):
        following = FollowingRelationships.objects.filter(user_follower=obj.profile)
        return FollowSerializer(following, many=True).data
    

    def get_reviews(self, obj):
        reviews = obj.reviews.all()
        return ReviewSerializer(reviews, many=True).data
    
    def get_user(self, obj):
        user = User.objects.get(email=obj.profile)
        return BasicUserSerializer(user, context=self.context).data

    def create(self, validated_data):
        """
        Custom create method to handle automatic creation of CompanyCategory and CompanySize.
        """
        # Extract organization_type and company_size from validated data
        organization_type_name = validated_data.pop('organization_type', None)
        company_size_name = validated_data.pop('company_size', None)

        # Get or create CompanyCategory
        if organization_type_name:
            organization_type, created = CompanyCategory.objects.get_or_create(
                name=organization_type_name
            )
            validated_data['organization_type'] = organization_type
            if created:
                print(f"Created new CompanyCategory: {organization_type_name}")

        # Get or create CompanySize
        if company_size_name:
            company_size, created = CompanySize.objects.get_or_create(
                size=company_size_name
            )
            validated_data['company_size'] = company_size
            if created:
                print(f"Created new CompanySize: {company_size_name}")

        # Create the company with the resolved foreign keys
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """
        Custom update method to handle automatic creation of CompanyCategory and CompanySize.
        """
        # Extract organization_type and company_size from validated data
        organization_type_name = validated_data.pop('organization_type', None)
        company_size_name = validated_data.pop('company_size', None)

        # Get or create CompanyCategory
        if organization_type_name:
            organization_type, created = CompanyCategory.objects.get_or_create(
                name=organization_type_name
            )
            validated_data['organization_type'] = organization_type
            if created:
                print(f"Created new CompanyCategory: {organization_type_name}")

        # Get or create CompanySize
        if company_size_name:
            company_size, created = CompanySize.objects.get_or_create(
                size=company_size_name
            )
            validated_data['company_size'] = company_size
            if created:
                print(f"Created new CompanySize: {company_size_name}")

        # Update the company with the resolved foreign keys
        return super().update(instance, validated_data)


class PostImageSerializer(serializers.ModelSerializer):

    class Meta:
        model = PostImage
        fields = ['id', 'image', 'uploaded_at']


class PostSerializer(serializers.HyperlinkedModelSerializer):
    company = CompanySerializer(read_only=True)
    user = UserSerializer(read_only=True)
    likes = serializers.SerializerMethodField()
    comments = serializers.SerializerMethodField()
    images = serializers.SerializerMethodField()

    class Meta:
        model = Post
        fields = [
            'id', 'body', 'company', 'user', 'images', 'likes', 'comments', 'date_created', 'date_updated', 'allow_comments', 'status'
        ]
    
    def create(self, validated_data):
        images = validated_data.pop('images', [])
        post = Post.objects.create(**validated_data)
        for image in images:
            PostImage.objects.create(post=post, image=image)
        return post

    def get_images(self, obj):
        # Include all associated images for the post
        images = obj.images.all()
        return [image.image.url for image in images]

    def get_likes(self, obj):
        likes = obj.likes.all()
        return LikeSerializer(likes, many=True).data

    def get_comments(self, obj):
        comments = obj.comments.all()
        return CommentSerializer(comments, many=True).data


class PostListSerializer(PostSerializer):
    liked_by_user = serializers.BooleanField(read_only=True)
    liked_by_company = serializers.BooleanField(read_only=True)

    class Meta(PostSerializer.Meta):
        fields = PostSerializer.Meta.fields + ["liked_by_user", "liked_by_company"]


class FollowSerializer(serializers.ModelSerializer):
    user_follower = BasicUserSerializer(read_only=True)
    user_following = BasicUserSerializer(read_only=True)
    company_follower = BasicCompanySerializer(read_only=True)
    company_following = BasicCompanySerializer(read_only=True)

    class Meta:
        model = FollowingRelationships
        fields = ['id', 'user_follower', 'company_follower', 'user_following', 'company_following']


class FollowingRelationshipSerializer(serializers.ModelSerializer):
    user_id = serializers.IntegerField(source="user_following.id", read_only=True)
    first_name = serializers.CharField(source="user_following.first_name")
    company_id = serializers.IntegerField(source="company_following.id", read_only=True)
    company_name = serializers.CharField(source="company_following.company_name")

    class Meta:
        model = FollowingRelationships
        fields = ('id', "user_id", "first_name", "company_id", "company_name")


class FollowerRelationshipSerializer(serializers.ModelSerializer):
    user_id = serializers.IntegerField(source="user_follower.id", read_only=True)
    first_name = serializers.CharField(source="user_follower.first_name")
    company_id = serializers.IntegerField(source="company_follower.id", read_only=True)
    company_name = serializers.CharField(source="company_follower.company_name")

    class Meta:
        model = FollowingRelationships
        fields = ('id', "user_id", "first_name", "company_id", "company_name")


class CommentSerializer(serializers.ModelSerializer):
    post_id = serializers.IntegerField(source="post.id", read_only=True)
    commented_at = serializers.DateTimeField(read_only=True)
    user = UserSerializer(read_only=True)  # or StringRelatedField() if only basic user info is needed
    company = CompanySerializer(read_only=True)  # or StringRelatedField() if only basic company info is needed
    likes = serializers.SerializerMethodField()
    replies = serializers.SerializerMethodField()

    class Meta:
        model = Comment
        fields = ("id", "post_id", "content", "commented_at", "user", "company", "likes", "replies")
        
    def get_likes(self, obj):
        likes = obj.likes.all()[:10]  # Limiting to the first 10 likes, adjust as needed
        return LikeSerializer(likes, many=True).data
    
    def get_replies(self, obj):
        replies = obj.replies.all()  # You may add pagination if this list could grow large
        return ReplySerializer(replies, many=True).data


class ReplySerializer(serializers.ModelSerializer):
    post_id = serializers.IntegerField(source="post.id", read_only=True)
    comment_id = serializers.IntegerField(source="comment.id", read_only=True)
    replied_at = serializers.DateTimeField(read_only=True)
    user = UserSerializer(read_only=True) 
    company = CompanySerializer(read_only=True) 
    # likes = serializers.SerializerMethodField()

    class Meta:
        model = Reply
        fields = ("id", "post_id", "content", "replied_at", "user", "company", "comment_id")
        
    # def get_likes(self, obj):
    #     likes = obj.likes.all() 
    #     return LikeSerializer(likes, many=True).data


class LikeSerializer(serializers.ModelSerializer):
    liked_by_user = serializers.CharField(source="user.first_name", read_only=True)
    liked_by_company = serializers.CharField(source="company.company_name", read_only=True)
    post_id = serializers.IntegerField(source="post.id", read_only=True)
    product_id = serializers.IntegerField(source="product.id", read_only=True)
    service_id = serializers.IntegerField(source="service.id", read_only=True)
    company_id = serializers.IntegerField(source="company.id", read_only=True)
    comment_id = serializers.IntegerField(source="comment.id", read_only=True)
    user = UserSerializer(read_only=True)

    class Meta:
        model = Like
        fields = (
            "id", "liked_by_user", "liked_by_company", 'post_id', 'user', 'company_id', 'product_id', 'service_id', "comment_id"
        )


class RepresentativeCategorySerializer(serializers.ModelSerializer):

    class Meta:
        model = RepresentativeCategory
        fields = '__all__'


class RepresentativeSerializer(serializers.ModelSerializer):
    # Use PrimaryKeyRelatedField for writable foreign key relationships
    user = serializers.PrimaryKeyRelatedField(queryset=User.objects.all())
    company = serializers.PrimaryKeyRelatedField(queryset=Company.objects.all())
    category = serializers.PrimaryKeyRelatedField(queryset=RepresentativeCategory.objects.all())

    class Meta:
        model = Representative
        fields = ['id', 'user', 'company', 'category', 'status', 'date_created', 'date_updated', 'slug', 'invited', 'token', 'expires_at']
