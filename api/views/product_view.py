from django.shortcuts import get_object_or_404
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import PermissionDenied
from rest_framework.filters import SearchFilter
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from api.models.comment_model import Like
from api.models.company_model import Company
from api.models.product_model import Product, ProductCategory, ProductImage
from api.permissions import IsCompanyOwnerOrReadOnly
from api.serializers.all_serializer import (
    ProductCategorySerializer,
    ProductImageSerializer,
    ProductSerializer,
)
from authentication.models import User
from notification.notification_service import NotificationService
from responselib.helpers import apiResponse


class ProductPagination(PageNumberPagination):
    page_size = 12
    page_size_query_param = 'page_size'
    max_page_size = 20


class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.all().select_related('company').order_by('-date_created')
    serializer_class = ProductSerializer
    pagination_class = ProductPagination
    # permission_classes = [IsAuthenticated, IsCompanyOwnerOrReadOnly]
    filter_backends = [DjangoFilterBackend, SearchFilter]
    filterset_fields = ['category', 'company', 'featured', 'date_created']
    search_fields = ['^title', '^sub_title']
    ordering_fields = ['title', 'date_created']

    def create(self, request, *args, **kwargs):
        company_name = request.data.get('company')
        company = get_object_or_404(Company, company_name=company_name)

        # Ensure user owns the company
        if company.profile != request.user:
            raise PermissionDenied("You can only create products for your own company")

        image_ids = request.data.get('images', [])
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        product = serializer.save(company=company)

        # Attach images to product and mark as permanent
        if image_ids:
            ProductImage.objects.filter(id__in=image_ids).update(product=product, temporary=False)

        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def perform_update(self, serializer):
        # Ensure user owns the company associated with the product
        product = self.get_object()
        if product.company.profile != self.request.user:
            raise PermissionDenied("You can only edit your company's products")
        serializer.save()

    def perform_destroy(self, instance):
        # Ensure user owns the company associated with the product
        if instance.company.profile != self.request.user:
            raise PermissionDenied("You can only delete your company's products")
        instance.delete()

    @action(detail=True, methods=['post'], url_path='like')
    def like_product(self, request, pk=None):
        product = self.get_object()
        user = request.user
        company = Company.objects.get(company_name=self.request.data.get('company'))
        recipient = User.objects.get(companies__id=company.id)

        if not user.is_authenticated:
            return Response({"detail": "Authentication is required."}, status=status.HTTP_401_UNAUTHORIZED)

        like, created = Like.objects.get_or_create(
            product=product,
            user=user,
            company_id=company.id
        )
        if not created:
            return Response({"detail": "Already liked this product."}, status=status.HTTP_400_BAD_REQUEST)
        
        # Create notification for the post owner
        NotificationService.create_notification(
            recipient=user,
            sender=user,
            notification_type='bookmark',
            title=product.title,
            message=f"You bookmarked {product.title}.",
            link=f"/products/{product.id}/"
        )
        if user.id != recipient.id:
            # Create notification for the product owner
            NotificationService.create_notification(
                recipient=recipient,
                sender=user,
                notification_type='bookmark',
                title=product.title,
                message=f"{user.first_name} bookmarked your product {product.title.lower()}.",
                link=f"/products/{product.id}/"
            )
        return Response(apiResponse(message=f"{product.title} has been added to bookmark"), status=status.HTTP_201_CREATED)
      
    @action(detail=True, methods=['post'], url_path='unlike')
    def unlike_product(self, request, pk=None):
        product = self.get_object()
        user = request.user

        if not user.is_authenticated:
            return Response({"detail": "Authentication is required."}, status=status.HTTP_401_UNAUTHORIZED)

        like = Like.objects.filter(product=product, user=user).first()
        if not like:
            return Response({"detail": "You have not liked this product."}, status=status.HTTP_400_BAD_REQUEST)
        like.delete()
        return Response(apiResponse(message=f"{product.title} has been removed from bookmark"), status=status.HTTP_204_NO_CONTENT)


class ProductCategoryViewSet(viewsets.ModelViewSet):
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer


class ProductImageViewSet(viewsets.ModelViewSet):
    queryset = ProductImage.objects.all()
    serializer_class = ProductImageSerializer
    pagination_class = ProductPagination
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['product']
